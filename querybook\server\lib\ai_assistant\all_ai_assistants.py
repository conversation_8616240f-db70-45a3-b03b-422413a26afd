from lib.utils.import_helper import import_module_with_default

# Import built-in assistants
try:
    from .assistants.openai_assistant import GeminiAssistant
    BUILT_IN_ASSISTANTS = [GeminiAssistant()]
except ImportError as e:
    print(f"Warning: Could not import built-in assistants: {e}")
    BUILT_IN_ASSISTANTS = []

# Import plugin assistants
ALL_PLUGIN_AI_ASSISTANTS = import_module_with_default(
    "ai_assistant_plugin",
    "ALL_PLUGIN_AI_ASSISTANTS",
    default=[],
)

# Combine built-in and plugin assistants
ALL_AI_ASSISTANTS = BUILT_IN_ASSISTANTS + ALL_PLUGIN_AI_ASSISTANTS


def get_ai_assistant_class(name: str):
    for assistant in ALL_AI_ASSISTANTS:
        if assistant.name == name:
            return assistant
    raise ValueError(f"Unknown AI assistant name {name}")
