#!/bin/bash

# Fix script for "No current event loop in thread" error
# This script sets up the <PERSON> assistant to avoid async issues

echo "=================================================="
echo "Fixing Event Loop Error for Gemini Assistant"
echo "=================================================="

# Check if we're in the right directory
if [ ! -d "querybook" ]; then
    echo "❌ Error: Please run this script from the querybook root directory"
    exit 1
fi

echo "✓ Running from querybook root directory"

# Set API key if provided as argument
if [ ! -z "$1" ]; then
    echo "Setting GOOGLE_API_KEY environment variable..."
    export GOOGLE_API_KEY="$1"
    echo "✓ GOOGLE_API_KEY set"
else
    if [ -z "$GOOGLE_API_KEY" ]; then
        echo "⚠ GOOGLE_API_KEY not set. You can:"
        echo "  1. Run: export GOOGLE_API_KEY='your-api-key-here'"
        echo "  2. Or run this script with your key: ./fix_event_loop_error.sh 'your-api-key'"
        echo "  3. Get your key from: https://makersuite.google.com/app/apikey"
    else
        echo "✓ GOOGLE_API_KEY is already set"
    fi
fi

# Test the fix
echo ""
echo "Testing the event loop fix..."
python test_event_loop_fix_simple.py

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 SUCCESS! Event loop issue is fixed!"
    echo ""
    echo "The Gemini assistant now uses DirectGeminiLLM which:"
    echo "✓ Makes direct HTTP requests to Google's API"
    echo "✓ Avoids async operations that cause event loop issues"
    echo "✓ Works in Querybook's synchronous Flask environment"
    echo ""
    echo "Next steps:"
    echo "1. Make sure GOOGLE_API_KEY is set in your environment"
    echo "2. Restart your Querybook server completely"
    echo "3. Test the AI features in the Querybook UI"
    echo ""
    echo "Your Querybook should now work with Gemini AI without event loop errors!"
else
    echo ""
    echo "❌ Some issues remain. Check the test output above."
    echo ""
    echo "Common solutions:"
    echo "1. Install required packages: pip install requests langchain-core"
    echo "2. Set GOOGLE_API_KEY environment variable"
    echo "3. Make sure you're in the querybook root directory"
fi
