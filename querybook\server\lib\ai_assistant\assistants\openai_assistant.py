import os
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI

from lib.ai_assistant.base_ai_assistant import BaseAIAssistant
from lib.logger import get_logger

LOG = get_logger(__file__)


GEMINI_MODEL_CONTEXT_WINDOW_SIZE = {
    # Gemini models
    "gemini-2.0-flash": 1048576,  # 1M tokens
    "gemini-1.5-pro": 2097152,    # 2M tokens
    "gemini-1.5-flash": 1048576,  # 1M tokens
    "gemini-1.0-pro": 32768,      # 32K tokens
}
DEFAULT_MODEL_NAME = "gemini-2.0-flash"


class OpenAIAssistant(BaseAIAssistant):
    """To use it, please set the following environment variable:
    GOOGLE_API_KEY: Google Gemini API key
    """

    def __init__(self):
        super().__init__()
        # Set the API key from environment variable if not already set
        if not os.environ.get('GEMINI_API_KEY'):
            # You can set your API key here or use environment variable
            # os.environ['GOOGLE_API_KEY'] = 'your-api-key-here'
            pass

    @property
    def name(self) -> str:
        return "gemini"

    def _get_context_length_by_model(self, model_name: str) -> int:
        return (
            GEMINI_MODEL_CONTEXT_WINDOW_SIZE.get(model_name)
            or GEMINI_MODEL_CONTEXT_WINDOW_SIZE[DEFAULT_MODEL_NAME]
        )

    def _get_default_llm_config(self):
        default_config = super()._get_default_llm_config()
        if not default_config.get("model_name"):
            default_config["model_name"] = DEFAULT_MODEL_NAME

        return default_config

    def _get_token_count(self, ai_command: str, prompt: str) -> int:
        """Estimate token count for Gemini models.

        Since Gemini doesn't have a direct tokenizer like tiktoken,
        we'll use a rough estimation: ~4 characters per token.
        """
        return len(prompt) // 4

    def _get_error_msg(self, error) -> str:
        # Handle Google API specific errors
        error_str = str(error)
        if "401" in error_str or "authentication" in error_str.lower():
            return "Invalid Google API key"
        elif "403" in error_str or "forbidden" in error_str.lower():
            return "Google API access forbidden"
        elif "429" in error_str or "quota" in error_str.lower():
            return "Google API rate limit exceeded"

        return super()._get_error_msg(error)

    def _get_llm(self, ai_command: str, prompt_length: int):
        config = self._get_llm_config(ai_command)

        # Set default model if not specified
        if "model_name" not in config:
            config["model_name"] = DEFAULT_MODEL_NAME

        # Map model_name to model for ChatGoogleGenerativeAI
        model_name = config.pop("model_name", DEFAULT_MODEL_NAME)
        config["model"] = model_name

        return ChatGoogleGenerativeAI(**config)
