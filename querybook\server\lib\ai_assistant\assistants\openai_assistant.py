import os
import asyncio
import google.generativeai as genai
from langchain_google_genai import ChatGoogleGenerativeAI
from lib.ai_assistant.base_ai_assistant import BaseAIAssistant
from lib.logger import get_logger

LOG = get_logger(__file__)

GEMINI_MODEL_CONTEXT_WINDOW_SIZE = {
    # Current Gemini models
    "gemini-2.0-flash-exp": 1048576,    # 1M tokens
    "gemini-1.5-pro": 2097152,          # 2M tokens
    "gemini-1.5-pro-exp": 2097152,      # 2M tokens
    "gemini-1.5-flash": 1048576,        # 1M tokens
    "gemini-1.5-flash-8b": 1048576,     # 1M tokens
    "gemini-1.0-pro": 30720,            # ~30K tokens
    
    # Legacy names for compatibility
    "gemini-2.0-flash": 1048576,        # Same as gemini-2.0-flash-exp
    "models/gemini-1.5-pro": 2097152,   # Full model path
    "models/gemini-1.5-flash": 1048576, # Full model path
}

DEFAULT_MODEL_NAME = "gemini-2.0-flash"

class GeminiAssistant(BaseAIAssistant):
    """Google Gemini assistant using LangChain ChatGoogleGenerativeAI.
    
    To use it, please set the following environment variable:
    GOOGLE_API_KEY: Google Gemini API key
    
    Or pass it directly in the config.
    """
    
    def __init__(self):
        super().__init__()
        self._setup_api_key()
        self._ensure_event_loop()
    
    def _setup_api_key(self):
        """Setup Google API key and validate."""
        api_key = os.environ.get('GOOGLE_API_KEY')
        
        if not api_key:
            # Fallback API key (remove this in production!)
            api_key = 'AIzaSyCKHLCrRFIlREEr37RMuqf83E0ezWxdghY'
            os.environ['GOOGLE_API_KEY'] = api_key
            LOG.warning("Using fallback API key. Set GOOGLE_API_KEY environment variable.")
        
        # Configure the genai client
        try:
            genai.configure(api_key=api_key)
        except Exception as e:
            LOG.error(f"Failed to configure Google API: {e}")
    
    def _ensure_event_loop(self):
        """Ensure there's an event loop in the current thread."""
        try:
            # Try to get the current event loop
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                # If the loop is closed, create a new one
                asyncio.set_event_loop(asyncio.new_event_loop())
        except RuntimeError:
            # No event loop exists, create one
            try:
                asyncio.set_event_loop(asyncio.new_event_loop())
                LOG.debug("Created new event loop for Gemini assistant")
            except Exception as e:
                LOG.warning(f"Could not create event loop: {e}")
    
    @property
    def name(self) -> str:
        return "gemini"
    
    def _get_context_length_by_model(self, model_name: str) -> int:
        """Get context window size for a given Gemini model."""
        # Clean model name (remove 'models/' prefix if present)
        clean_name = model_name.replace('models/', '')
        
        return (
            GEMINI_MODEL_CONTEXT_WINDOW_SIZE.get(model_name) or
            GEMINI_MODEL_CONTEXT_WINDOW_SIZE.get(clean_name) or
            GEMINI_MODEL_CONTEXT_WINDOW_SIZE[DEFAULT_MODEL_NAME]
        )
    
    def _get_default_llm_config(self):
        """Get default LLM configuration."""
        default_config = super()._get_default_llm_config()
        
        # Set default model if not specified
        if not default_config.get("model_name") and not default_config.get("model"):
            default_config["model_name"] = DEFAULT_MODEL_NAME
        
        # Add some Gemini-specific defaults
        if "temperature" not in default_config:
            default_config["temperature"] = 0.7
            
        return default_config
    
    def _get_token_count(self, ai_command: str, prompt: str) -> int:
        """Estimate token count for Gemini models.
        
        Gemini doesn't have a direct tokenizer like tiktoken,
        so we use estimation methods.
        """
        try:
            config = self._get_llm_config(ai_command)
            model_name = config.get("model_name") or config.get("model", DEFAULT_MODEL_NAME)
            
            # Method 1: Use Google's count_tokens API if available
            try:
                # Clean model name
                clean_model = model_name.replace('models/', '')
                if not clean_model.startswith('gemini-'):
                    clean_model = f"models/{clean_model}"
                else:
                    clean_model = f"models/{clean_model}"
                
                model = genai.GenerativeModel(clean_model)
                response = model.count_tokens(prompt)
                return response.total_tokens
            except Exception as e:
                LOG.debug(f"Could not use count_tokens API: {e}")
                
                # Method 2: Rough estimation based on model type
                if "1.5" in model_name or "2.0" in model_name:
                    # Newer models: ~3.5 characters per token
                    return len(prompt) // 3.5
                else:
                    # Older models: ~4 characters per token
                    return len(prompt) // 4
                    
        except Exception as e:
            LOG.error(f"Error counting tokens: {e}")
            # Final fallback: 4 chars per token
            return len(prompt) // 4
    
    def _get_error_msg(self, error) -> str:
        """Get user-friendly error message from Google API errors."""
        error_str = str(error).lower()
        
        if "401" in error_str or "authentication" in error_str or "api key" in error_str:
            return "Invalid Google API key"
        elif "403" in error_str or "forbidden" in error_str:
            return "Google API access forbidden - check your API key permissions"
        elif "429" in error_str or "quota" in error_str or "rate limit" in error_str:
            return "Google API rate limit exceeded"
        elif "400" in error_str or "invalid request" in error_str:
            return f"Invalid request to Google API: {str(error)}"
        elif "model not found" in error_str:
            return "Gemini model not found - check model name"
        elif "safety" in error_str:
            return "Content blocked by Gemini safety filters"
        else:
            return super()._get_error_msg(error)
    
    def _get_llm(self, ai_command: str, prompt_length: int):
        """Create and return ChatGoogleGenerativeAI instance."""
        # Ensure event loop exists before creating LLM
        self._ensure_event_loop()
        
        config = self._get_llm_config(ai_command)
        
        # Handle model name mapping
        if "model_name" in config:
            model_name = config.pop("model_name")
            config["model"] = model_name
        elif not config.get("model"):
            config["model"] = DEFAULT_MODEL_NAME
        
        # Add reasonable defaults
        if "temperature" not in config:
            config["temperature"] = 0.7
        
        if "max_output_tokens" not in config:
            # Set max tokens based on context window and prompt length
            context_window = self._get_context_length_by_model(config["model"])
            # Reserve tokens for response (Gemini uses max_output_tokens)
            config["max_output_tokens"] = min(8192, max(1024, context_window - prompt_length - 1000))
        
        try:
            return ChatGoogleGenerativeAI(**config)
        except RuntimeError as e:
            if "no current event loop" in str(e).lower():
                # Force create a new event loop and try again
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    return ChatGoogleGenerativeAI(**config)
                except Exception as retry_error:
                    LOG.error(f"Failed to create ChatGoogleGenerativeAI after event loop fix: {retry_error}")
                    raise retry_error
            else:
                LOG.error(f"Error creating ChatGoogleGenerativeAI instance: {e}")
                raise e
        except Exception as e:
            LOG.error(f"Error creating ChatGoogleGenerativeAI instance: {e}")
            raise
    
    def get_available_models(self) -> list:
        """Get list of available Gemini models."""
        return list(GEMINI_MODEL_CONTEXT_WINDOW_SIZE.keys())
    
    def is_model_supported(self, model_name: str) -> bool:
        """Check if a model is supported."""
        clean_name = model_name.replace('models/', '')
        return (model_name in GEMINI_MODEL_CONTEXT_WINDOW_SIZE or 
                clean_name in GEMINI_MODEL_CONTEXT_WINDOW_SIZE)
    
    def get_model_info(self, model_name: str) -> dict:
        """Get information about a specific Gemini model."""
        if not self.is_model_supported(model_name):
            return {"error": f"Model {model_name} is not supported"}
        
        return {
            "name": model_name,
            "context_window": self._get_context_length_by_model(model_name),
            "is_default": model_name == DEFAULT_MODEL_NAME,
            "provider": "Google Gemini"
        }
    
    def list_available_models_from_api(self) -> list:
        """Get list of models directly from Google API."""
        try:
            models = []
            for model in genai.list_models():
                if 'generateContent' in model.supported_generation_methods:
                    models.append({
                        "name": model.name,
                        "display_name": model.display_name,
                        "description": getattr(model, 'description', ''),
                    })
            return models
        except Exception as e:
            LOG.error(f"Failed to list models from API: {e}")
            return []