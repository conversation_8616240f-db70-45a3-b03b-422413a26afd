import os
import requests
import json
from typing import Any, List, Optional

from lib.ai_assistant.base_ai_assistant import BaseAIAssistant
from lib.logger import get_logger

LOG = get_logger(__file__)


GEMINI_MODEL_CONTEXT_WINDOW_SIZE = {
    # Gemini models
    "gemini-2.0-flash": 1048576,  # 1M tokens
    "gemini-1.5-pro": 2097152,    # 2M tokens
    "gemini-1.5-flash": 1048576,  # 1M tokens
    "gemini-1.0-pro": 32768,      # 32K tokens
}
DEFAULT_MODEL_NAME = "gemini-2.0-flash"


class SimpleLLM:
    """Simple LLM that avoids all LangChain dependencies and async operations."""

    def __init__(self, model: str = DEFAULT_MODEL_NAME, temperature: float = 0.7, **kwargs):
        self.model = model
        self.temperature = temperature
        self.api_key = os.environ.get("GOOGLE_API_KEY")

        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY environment variable must be set")

    def __call__(self, *args, **kwargs):
        """Make the class callable."""
        if args:
            return self.invoke(args[0], **kwargs)
        return self.invoke("", **kwargs)

    def invoke(self, input, config=None, **kwargs):
        """Invoke the model with input."""
        if isinstance(input, str):
            prompt = input
        elif isinstance(input, list):
            # Handle list of messages
            prompt_parts = []
            for item in input:
                if hasattr(item, 'content'):
                    prompt_parts.append(str(item.content))
                else:
                    prompt_parts.append(str(item))
            prompt = "\n\n".join(prompt_parts)
        else:
            prompt = str(input)

        response_text = self._call_gemini_api(prompt)

        # Return an object that has a content attribute
        class SimpleResponse:
            def __init__(self, content):
                self.content = content

        return SimpleResponse(response_text)

    def predict(self, text: str, **kwargs) -> str:
        """Predict method for compatibility."""
        response = self.invoke(text, **kwargs)
        return response.content

    def __or__(self, other):
        """Support for | operator (pipe) used in LangChain chains."""
        # Return a simple chain-like object
        class SimpleChain:
            def __init__(self, llm, parser):
                self.llm = llm
                self.parser = parser

            def invoke(self, input, **kwargs):
                result = self.llm.invoke(input, **kwargs)

                # Handle different parser types
                parser_name = type(self.parser).__name__

                if parser_name == "JsonOutputParser" or "json" in parser_name.lower():
                    # Try to parse as JSON
                    try:
                        return json.loads(result.content)
                    except (json.JSONDecodeError, AttributeError):
                        # If parsing fails, return the raw content
                        return result.content

                elif parser_name == "StrOutputParser" or "str" in parser_name.lower():
                    # Return as string
                    return result.content

                else:
                    # For unknown parsers, try to call them
                    try:
                        if callable(self.parser):
                            return self.parser(result)
                        elif hasattr(self.parser, 'parse'):
                            return self.parser.parse(result.content)
                        elif hasattr(self.parser, 'invoke'):
                            return self.parser.invoke(result.content)
                        else:
                            return result.content
                    except Exception:
                        return result.content

            def stream(self, input, **kwargs):
                """Simple streaming implementation."""
                result = self.invoke(input, **kwargs)
                # For simplicity, just yield the result as a single chunk
                yield result

        return SimpleChain(self, other)

    def _call_gemini_api(self, prompt: str) -> str:
        """Make direct API call to Gemini."""
        url = f"https://generativelanguage.googleapis.com/v1beta/models/{self.model}:generateContent"

        headers = {
            "Content-Type": "application/json"
        }

        params = {
            "key": self.api_key
        }

        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": self.temperature
            }
        }

        try:
            response = requests.post(url, headers=headers, params=params, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()

            if "candidates" in result and len(result["candidates"]) > 0:
                candidate = result["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    return candidate["content"]["parts"][0]["text"]

            raise ValueError("No valid response from Gemini API")

        except requests.exceptions.RequestException as e:
            LOG.error(f"Gemini API request failed: {e}")
            raise Exception(f"Failed to call Gemini API: {str(e)}")
        except (KeyError, IndexError) as e:
            LOG.error(f"Unexpected Gemini API response format: {e}")
            raise Exception(f"Invalid response format from Gemini API: {str(e)}")


class GeminiAssistant(BaseAIAssistant):
    """To use it, please set the following environment variable:
    GOOGLE_API_KEY: Google Gemini API key
    """

    def __init__(self):
        super().__init__()
        # Ensure the API key is available
        if not os.environ.get('GOOGLE_API_KEY'):
            # You can set your API key here or use environment variable
            os.environ['GOOGLE_API_KEY'] = 'AIzaSyCKHLCrRFIlREEr37RMuqf83E0ezWxdghY'
            LOG.warning("GOOGLE_API_KEY environment variable is not set")

    @property
    def name(self) -> str:
        return "gemini"

    def _get_context_length_by_model(self, model_name: str) -> int:
        return (
            GEMINI_MODEL_CONTEXT_WINDOW_SIZE.get(model_name)
            or GEMINI_MODEL_CONTEXT_WINDOW_SIZE[DEFAULT_MODEL_NAME]
        )

    def _get_default_llm_config(self):
        default_config = super()._get_default_llm_config()
        if not default_config.get("model_name"):
            default_config["model_name"] = DEFAULT_MODEL_NAME

        return default_config

    def _get_token_count(self, ai_command: str, prompt: str) -> int:
        """Estimate token count for Gemini models.

        Since Gemini doesn't have a direct tokenizer like tiktoken,
        we'll use a rough estimation: ~4 characters per token.
        """
        return len(prompt) // 4

    def _get_error_msg(self, error) -> str:
        # Handle Google API specific errors
        error_str = str(error)
        if "401" in error_str or "authentication" in error_str.lower():
            return "Invalid Google API key"
        elif "403" in error_str or "forbidden" in error_str.lower():
            return "Google API access forbidden"
        elif "429" in error_str or "quota" in error_str.lower():
            return "Google API rate limit exceeded"

        return super()._get_error_msg(error)

    def _get_llm(self, ai_command: str, prompt_length: int):
        config = self._get_llm_config(ai_command)

        # Set default model if not specified
        if "model_name" not in config:
            config["model_name"] = DEFAULT_MODEL_NAME

        # Map model_name to model for SimpleLLM
        model_name = config.pop("model_name", DEFAULT_MODEL_NAME)
        config["model"] = model_name

        # Use our simple implementation to completely avoid async issues
        return SimpleLLM(**config)
