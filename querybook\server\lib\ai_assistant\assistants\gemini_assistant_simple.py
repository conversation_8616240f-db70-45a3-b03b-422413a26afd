import os
import requests
import json
from typing import Any, List, Optional
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.outputs import LLMResult, Generation
from langchain_core.callbacks.manager import Callback<PERSON>anager<PERSON><PERSON><PERSON><PERSON><PERSON>

from lib.ai_assistant.base_ai_assistant import Base<PERSON><PERSON>ssistant
from lib.logger import get_logger

LOG = get_logger(__file__)


GEMINI_MODEL_CONTEXT_WINDOW_SIZE = {
    # Gemini models
    "gemini-2.0-flash": 1048576,  # 1M tokens
    "gemini-1.5-pro": 2097152,    # 2M tokens
    "gemini-1.5-flash": 1048576,  # 1M tokens
    "gemini-1.0-pro": 32768,      # 32K tokens
}
DEFAULT_MODEL_NAME = "gemini-2.0-flash"


class SimpleGeminiLLM(BaseLanguageModel):
    """Simple Gemini LLM that uses direct API calls to avoid async issues."""
    
    def __init__(self, model: str = DEFAULT_MODEL_NAME, temperature: float = 0.7, **kwargs):
        super().__init__(**kwargs)
        self.model = model
        self.temperature = temperature
        self.api_key = os.environ.get("GOOGLE_API_KEY")
        
        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY environment variable must be set")
    
    @property
    def _llm_type(self) -> str:
        return "simple_gemini"
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> LLMResult:
        """Generate response from Gemini API."""
        prompt = self._messages_to_prompt(messages)
        response_text = self._call_gemini_api(prompt)
        
        generation = Generation(text=response_text)
        return LLMResult(generations=[[generation]])
    
    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """Call Gemini API with a prompt."""
        return self._call_gemini_api(prompt)
    
    def invoke(self, input, config=None, **kwargs):
        """Invoke the model with input."""
        if isinstance(input, str):
            response_text = self._call_gemini_api(input)
        elif isinstance(input, list):
            prompt = self._messages_to_prompt(input)
            response_text = self._call_gemini_api(prompt)
        else:
            prompt = str(input)
            response_text = self._call_gemini_api(prompt)
        
        # Return an object that has a content attribute (like AIMessage)
        class SimpleResponse:
            def __init__(self, content):
                self.content = content
        
        return SimpleResponse(response_text)
    
    def _messages_to_prompt(self, messages: List[BaseMessage]) -> str:
        """Convert LangChain messages to a single prompt string."""
        if len(messages) == 1 and isinstance(messages[0], HumanMessage):
            return messages[0].content
        
        # For multiple messages, concatenate them
        prompt_parts = []
        for message in messages:
            if isinstance(message, HumanMessage):
                prompt_parts.append(f"Human: {message.content}")
            elif isinstance(message, AIMessage):
                prompt_parts.append(f"Assistant: {message.content}")
            else:
                prompt_parts.append(str(message.content))
        
        return "\n\n".join(prompt_parts)
    
    def _call_gemini_api(self, prompt: str) -> str:
        """Make API call to Gemini."""
        url = f"https://generativelanguage.googleapis.com/v1beta/models/{self.model}:generateContent"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        params = {
            "key": self.api_key
        }
        
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": self.temperature
            }
        }
        
        try:
            response = requests.post(url, headers=headers, params=params, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if "candidates" in result and len(result["candidates"]) > 0:
                candidate = result["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    return candidate["content"]["parts"][0]["text"]
            
            raise ValueError("No valid response from Gemini API")
            
        except requests.exceptions.RequestException as e:
            LOG.error(f"Gemini API request failed: {e}")
            raise Exception(f"Failed to call Gemini API: {str(e)}")
        except (KeyError, IndexError) as e:
            LOG.error(f"Unexpected Gemini API response format: {e}")
            raise Exception(f"Invalid response format from Gemini API: {str(e)}")


class SimpleGeminiAssistant(BaseAIAssistant):
    """Simple Gemini Assistant that avoids async issues by using direct API calls."""
    
    def __init__(self):
        super().__init__()
        # Ensure the API key is available
        if not os.environ.get('GOOGLE_API_KEY'):
            LOG.warning("GOOGLE_API_KEY environment variable is not set")

    @property
    def name(self) -> str:
        return "gemini"

    def _get_context_length_by_model(self, model_name: str) -> int:
        return (
            GEMINI_MODEL_CONTEXT_WINDOW_SIZE.get(model_name)
            or GEMINI_MODEL_CONTEXT_WINDOW_SIZE[DEFAULT_MODEL_NAME]
        )

    def _get_default_llm_config(self):
        default_config = super()._get_default_llm_config()
        if not default_config.get("model_name"):
            default_config["model_name"] = DEFAULT_MODEL_NAME

        return default_config

    def _get_token_count(self, ai_command: str, prompt: str) -> int:
        """Estimate token count for Gemini models.
        
        Since Gemini doesn't have a direct tokenizer like tiktoken,
        we'll use a rough estimation: ~4 characters per token.
        """
        return len(prompt) // 4

    def _get_error_msg(self, error) -> str:
        # Handle Google API specific errors
        error_str = str(error)
        if "401" in error_str or "authentication" in error_str.lower():
            return "Invalid Google API key"
        elif "403" in error_str or "forbidden" in error_str.lower():
            return "Google API access forbidden"
        elif "429" in error_str or "quota" in error_str.lower():
            return "Google API rate limit exceeded"

        return super()._get_error_msg(error)

    def _get_llm(self, ai_command: str, prompt_length: int):
        config = self._get_llm_config(ai_command)
        
        # Set default model if not specified
        if "model_name" not in config:
            config["model_name"] = DEFAULT_MODEL_NAME
        
        # Map model_name to model for SimpleGeminiLLM
        model_name = config.pop("model_name", DEFAULT_MODEL_NAME)
        config["model"] = model_name

        # Use our simple implementation to avoid async issues
        return SimpleGeminiLLM(**config)
